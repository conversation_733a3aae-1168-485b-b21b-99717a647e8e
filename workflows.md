# Workflows

## List of available workflows

### createNewSite

Creates a new Impact Hub site from a wptemplate.impacthub.net site on WPMU Dev.

#### Required arguments

- `--city` (string): The city name for the new site.

#### Optional arguments

- `--contactEmail` (string): The contact email address for the new site.

#### Blocks used

- `wpmu-hub/cloneSiteViaUI.js`
- `wpmu-hub/changeLabelColour.js`
- `wordpress/elementorLicenses.js`
- `wordpress/createUser.js`
- `wordpress/deleteUsers.js`
- `monday/addNewHub.js`
- `email/sendEmail.js`

#### Flow in words

1. Clone the template site from WPMU Dev.
2. Make it green filter in WPMU
3. Add a line to "Template migration tracking" Board in Monday (add a schema file for tables?)
4. Deactivate and reactivate the elementor license so it's applied to the new site.
5. Create a new WordPress user for the site (username: --city, password: random generated, email: --<EMAIL>, user role: admin)
6. Delete all users excepts ulys<PERSON>.coates and city user
7. Send a welcome email to the contact email address. (use newWebsiteWelcomeEmail template)

## List of planned workflows

### migrateHubToWPTemplate

### closeHub

### upAndRunning
